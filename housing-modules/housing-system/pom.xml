<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>org.housing</groupId>
        <artifactId>housing-modules</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>housing-system</artifactId>

    <description>
        system系统模块
    </description>

    <dependencies>
        <!-- 通用工具-->
        <dependency>
            <groupId>org.housing</groupId>
            <artifactId>housing-common-core</artifactId>
        </dependency>

        <dependency>
            <groupId>org.housing</groupId>
            <artifactId>housing-common-doc</artifactId>
        </dependency>

        <dependency>
            <groupId>org.housing</groupId>
            <artifactId>housing-common-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>org.housing</groupId>
            <artifactId>housing-common-translation</artifactId>
        </dependency>

        <!-- OSS功能模块 -->
        <dependency>
            <groupId>org.housing</groupId>
            <artifactId>housing-common-oss</artifactId>
        </dependency>

        <dependency>
            <groupId>org.housing</groupId>
            <artifactId>housing-common-log</artifactId>
        </dependency>

        <!-- excel-->
        <dependency>
            <groupId>org.housing</groupId>
            <artifactId>housing-common-excel</artifactId>
        </dependency>
        <dependency>
            <groupId>org.housing</groupId>
            <artifactId>housing-common-tenant</artifactId>
        </dependency>

        <dependency>
            <groupId>org.housing</groupId>
            <artifactId>housing-common-security</artifactId>
        </dependency>

        <dependency>
            <groupId>org.housing</groupId>
            <artifactId>housing-common-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.housing</groupId>
            <artifactId>housing-common-idempotent</artifactId>
        </dependency>

        <dependency>
            <groupId>org.housing</groupId>
            <artifactId>housing-common-sensitive</artifactId>
        </dependency>

        <dependency>
            <groupId>org.housing</groupId>
            <artifactId>housing-common-encrypt</artifactId>
        </dependency>

        <dependency>
            <groupId>org.housing</groupId>
            <artifactId>housing-common-websocket</artifactId>
        </dependency>

        <dependency>
            <groupId>org.housing</groupId>
            <artifactId>housing-common-mail</artifactId>
        </dependency>
        <!-- docx4j -->
        <dependency>
            <groupId>org.docx4j</groupId>
            <artifactId>docx4j-JAXB-ReferenceImpl</artifactId>
        </dependency>
        <dependency>
            <groupId>org.docx4j</groupId>
            <artifactId>docx4j-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.docx4j</groupId>
            <artifactId>docx4j-JAXB-MOXy</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>wx-java-mp-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>wx-java-miniapp-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>

        <!-- Barcode4J for barcode generation -->
        <dependency>
            <groupId>net.sf.barcode4j</groupId>
            <artifactId>barcode4j</artifactId>
            <version>2.1</version>
        </dependency>

        <!-- iText 7 for HTML to PDF -->
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>html2pdf</artifactId>
            <version>4.0.5</version>
        </dependency>
        <!-- 添加字体支持 -->
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>font-asian</artifactId>
            <version>7.2.5</version>
        </dependency>
    </dependencies>

</project>
