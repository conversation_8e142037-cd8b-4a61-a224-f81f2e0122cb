<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="zh-CN">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta charset="UTF-8" />
    <title>业主意见征询表</title>
    <style>


        body {
            font-family: "SimSun", "宋体", "Microsoft YaHei", "微软雅黑", serif;
            font-size: 12px;
            line-height: 1.5;
            margin: 0;
            padding: 20px;
            background: white;
        }

        .page {
            width: 100%;
            margin: 0 auto;
            background: white;
            padding: 20px;
            min-height: 800px;
        }

        .page-break {
            page-break-before: always;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 15px;
        }

        .title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .basic-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .basic-info span {
            flex: 1;
        }

        .satisfaction-section {
            margin-bottom: 30px;
        }

        .satisfaction-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            table-layout: fixed;
        }

        .satisfaction-table th,
        .satisfaction-table td {
            border: 1px solid #000;
            padding: 8px;
            text-align: center;
            font-size: 12px;
            word-wrap: break-word;
        }

        .satisfaction-table th {
            background-color: #ffffff;
            font-weight: bold;
            color: #000000;
        }

        .satisfaction-table .item-name {
            text-align: left;
            width: 200px;
            font-weight: normal;
        }

        .satisfaction-table .question-header {
            text-align: center;
            width: 200px;
            font-weight: bold;
            color: #000000;
        }

        .satisfaction-table .option-header {
            text-align: center;
            width: 80px;
            font-weight: bold;
            color: #000000;
        }

        .satisfaction-table .option-cell {
            text-align: center;
            width: 80px;
        }

        .checkbox {
            width: 15px;
            height: 15px;
            border: 1px solid #000;
            display: inline-block;
            text-align: center;
            line-height: 13px;
            margin: 0 2px;
        }

        .checkbox.checked::before {
            content: "✓";
            font-weight: bold;
            color: #000000;
        }

        .question-section {
            margin-bottom: 20px;
        }

        .question-title {
            font-weight: bold;
            margin-bottom: 10px;
            font-size: 14px;
            color: #000000;
        }

        .question-item {
            margin-bottom: 15px;
            padding: 10px;
            border: 1px solid #ddd;
        }

        .text-answer {
            border-bottom: 1px solid #000;
            min-height: 20px;
            padding: 5px;
            margin-top: 5px;
        }

        .signature-section {
            margin-top: 40px;
        }

        .signature-item {
            margin-bottom: 30px;
            position: relative;
        }

        .signature-label {
            display: inline-block;
            background: white;
            padding-right: 10px;
            position: relative;
            z-index: 1;
        }

        .signature-line {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 1px;
            border-bottom: 1px solid #000;
        }

        .signature-image {
            display: inline-block;
            margin-left: 20px;
        }

        .sign-img {
            max-width: 150px;
            max-height: 80px;
            border: 1px solid #ddd;
            padding: 5px;
        }

        .date-section {
            text-align: right;
            margin-top: 30px;
        }

        .underline {
            border-bottom: 1px solid #000;
            display: inline-block;
            min-width: 100px;
            text-align: center;
        }

        /* 确保所有元素都使用支持中文的字体 */
        * {
            font-family: "SimSun", "宋体", "Microsoft YaHei", "微软雅黑", serif;
        }

    </style>
</head>
<body>
    <#list dataList as data>
    <div class="page <#if data_index gt 0>page-break</#if>">
        <!-- 页面头部 -->
        <div class="header">
            <div class="title">${data.questionTitle}</div>
        </div>

        <!-- 基本信息 -->
        <div class="basic-info">
            <span>物业名称：<span class="underline">${data.housingName!''}</span></span>
            <span>房号：<span class="underline">${data.houseInfo!''}</span></span>
            <span>联系方式：<span class="underline">${data.ownerPhone!''}</span></span>
        </div>

        <!-- 选择题和多选题表格 -->
        <#assign choiceQuestions = []>
        <#list data.answersInfos as answerInfo>
            <#if (answerInfo.itemType?number == 1) || (answerInfo.itemType?number == 3)>
                <#assign choiceQuestions = choiceQuestions + [answerInfo]>
            </#if>
        </#list>

        <#if (choiceQuestions?size > 0)>
        <div class="satisfaction-section">
            <table class="satisfaction-table">
                <thead>
                    <tr>
                        <th class="question-header">题目</th>
                        <#list data.questionOptions as option>
                        <th class="option-header">${option}</th>
                        </#list>
                    </tr>
                </thead>
                <tbody>
                    <#list choiceQuestions as answerInfo>
                    <tr>
                        <td class="item-name">${answerInfo.itemContent}</td>
                        <#list data.questionOptions as option>
                        <td class="option-cell">
                            <#assign isChecked = false>
                            <#if answerInfo.answer??>
                                <#if answerInfo.itemType?number == 1>
                                    <#-- 单选题：检查答案是否等于当前选项 -->
                                    <#if answerInfo.answer == option>
                                        <#assign isChecked = true>
                                    </#if>
                                <#elseif answerInfo.itemType?number == 3>
                                    <#-- 多选题：检查答案中是否包含当前选项 -->
                                    <#assign answerOptions = answerInfo.answer?split(',')>
                                    <#list answerOptions as selectedOption>
                                        <#if selectedOption?trim == option>
                                            <#assign isChecked = true>
                                        </#if>
                                    </#list>
                                </#if>
                            </#if>
                            <span class="checkbox <#if isChecked>checked</#if>"></span>
                        </td>
                        </#list>
                    </tr>
                    </#list>
                </tbody>
            </table>
        </div>
        </#if>

        <!-- 填空题部分 -->
        <#assign textQuestions = []>
        <#list data.answersInfos as answerInfo>
            <#if answerInfo.itemType?number == 2>
                <#assign textQuestions = textQuestions + [answerInfo]>
            </#if>
        </#list>

        <#if (textQuestions?size > 0)>
        <div class="question-section">
            <#list textQuestions as answerInfo>
            <div class="question-item">
                <div class="question-title">${answerInfo.itemContent}</div>
                <div class="text-answer">
                    <#if answerInfo.answer??>
                        ${answerInfo.answer}
                    </#if>
                </div>
            </div>
            </#list>
        </div>
        </#if>



        <!-- 签名区域 -->
        <#if data.signature?number == 1>
        <div class="signature-section">
            <div class="signature-item">
                <span class="signature-label">物业服务中心工作人员：</span>
                <div class="signature-line"></div>
            </div>
            <div class="signature-item">
                <span class="signature-label">业主本人签名：</span>
                <#if data.signImg??>
                    <div class="signature-image">
                        <img src="${data.signImg}" alt="业主签名" class="sign-img" />
                    </div>
                <#else>
                    <div class="signature-line"></div>
                </#if>
            </div>
        </div>
        </#if>

        <!-- 日期 -->
        <div class="date-section">
            <#if data.answerDate??>
                <span style="margin-right: 50px;">${data.answerDate?string('yyyy')}年</span>
                <span style="margin-right: 50px;">${data.answerDate?string('MM')}月</span>
                <span>${data.answerDate?string('dd')}日</span>
            <#else>
                <span style="margin-right: 50px;">年</span>
                <span style="margin-right: 50px;">月</span>
                <span>日</span>
            </#if>
        </div>
    </div>
    </#list>
</body>
</html>
