package org.housing.system.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.housing.common.core.validate.AddGroup;
import org.housing.common.core.validate.EditGroup;
import org.housing.common.mybatis.core.domain.BaseEntity;
import org.housing.system.domain.SysQuestionAnswers;

/**
 * 业主答卷主业务对象 sys_question_answers
 *
 * <AUTHOR>
 * @date 2025-07-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = SysQuestionAnswers.class, reverseConvertGenerate = false)
public class SysQuestionAnswersBo extends BaseEntity {

    /**
     * 答卷ID
     */
    @NotNull(message = "答卷ID不能为空", groups = { EditGroup.class })
    private Long answersId;

    /**
     * 关联问卷主表ID
     */
    @NotNull(message = "关联问卷主表ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long questionId;

    /**
     * 小区主键
     */
    @NotNull(message = "小区主键不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long housingId;

    /**
     * 业主唯一标识
     */
    @NotNull(message = "业主唯一标识不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long ownerId;

    /**
     * 电子签名图片路径
     */
    @NotBlank(message = "电子签名图片路径不能为空", groups = { AddGroup.class, EditGroup.class })
    private String signImg;


}
