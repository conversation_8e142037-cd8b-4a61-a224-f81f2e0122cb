package org.housing.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.template.Template;
import cn.hutool.extra.template.TemplateConfig;
import cn.hutool.extra.template.TemplateEngine;
import cn.hutool.extra.template.TemplateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.itextpdf.html2pdf.ConverterProperties;
import com.itextpdf.html2pdf.HtmlConverter;
import com.itextpdf.layout.font.FontProvider;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.housing.common.core.exception.ServiceException;
import org.housing.common.core.service.OssService;
import org.housing.common.core.utils.DateUtils;
import org.housing.common.core.utils.MapstructUtils;
import org.housing.common.core.utils.StringUtils;
import org.housing.common.mybatis.core.page.PageQuery;
import org.housing.common.mybatis.core.page.TableDataInfo;
import org.housing.system.domain.*;
import org.housing.system.domain.bo.SysQuestionBo;
import org.housing.system.domain.bo.SysQuestionItemsBo;
import org.housing.system.domain.vo.SysQuestionItemsVo;
import org.housing.system.domain.vo.SysQuestionOptionsVo;
import org.housing.system.domain.vo.SysQuestionVo;
import org.housing.system.mapper.*;
import org.housing.system.service.ISysQuestionService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 问卷管理Service业务层处理
 *
 * <AUTHOR> Bin
 * @date 2025-07-12
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class SysQuestionServiceImpl implements ISysQuestionService {

    private final SysQuestionMapper baseMapper;

    private final SysQuestionItemsMapper questionItemsMapper;

    private final SysQuestionOptionsMapper questionOptionsMapper;

    private final SysQuestionAnswersMapper questionAnswersMapper;

    private final SysQuestionAnswerDetailsMapper answerDetailsMapper;

    private final SysOwnerMapper ownerMapper;

    private final SysOwnerHouseMapper ownerHouseMapper;

    private final SysHousingMapper housingMapper;

    private final OssService ossService;

    /**
     * 查询问卷管理
     *
     * @param questionId 主键
     * @return 问卷管理
     */
    @Override
    public SysQuestionVo queryById(Long questionId){
        SysQuestionVo questionVo = baseMapper.selectVoById(questionId);
        return buildQuestionWithItems(questionVo);
    }

    /**
     * 分页查询问卷管理列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 问卷管理分页列表
     */
    @Override
    public TableDataInfo<SysQuestionVo> queryPageList(SysQuestionBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SysQuestion> lqw = buildQueryWrapper(bo);
        Page<SysQuestionVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);

        // 为分页结果中的每个问卷组装题目和选项数据
        List<SysQuestionVo> records = result.getRecords().stream()
            .map(this::buildQuestionWithItems)
            .toList();
        result.setRecords(records);

        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的问卷管理列表
     *
     * @param bo 查询条件
     * @return 问卷管理列表
     */
    @Override
    public List<SysQuestionVo> queryList(SysQuestionBo bo) {
        LambdaQueryWrapper<SysQuestion> lqw = buildQueryWrapper(bo);
        List<SysQuestionVo> questionList = baseMapper.selectVoList(lqw);

        // 为列表中的每个问卷组装题目和选项数据
        return questionList.stream()
            .map(this::buildQuestionWithItems)
            .toList();
    }

    private LambdaQueryWrapper<SysQuestion> buildQueryWrapper(SysQuestionBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SysQuestion> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getQuestionTitle()), SysQuestion::getQuestionTitle, bo.getQuestionTitle());
        lqw.eq(bo.getSignature() != null, SysQuestion::getSignature, bo.getSignature());
        lqw.eq(bo.getHousingId() != null, SysQuestion::getHousingId, bo.getHousingId());
        // lqw.le(bo.getQuestionStartTime() != null, SysQuestion::getQuestionStartTime, bo.getQuestionStartTime());
        // lqw.ge(bo.getQuestionEndTime() != null, SysQuestion::getQuestionEndTime, bo.getQuestionEndTime());
        lqw.orderByDesc(SysQuestion::getCreateTime);
        return lqw;
    }

    /**
     * 构建包含题目和选项的完整问卷数据
     *
     * @param questionVo 问卷基础信息
     * @return 包含题目和选项的完整问卷数据
     */
    private SysQuestionVo buildQuestionWithItems(SysQuestionVo questionVo) {
        if (questionVo == null || questionVo.getQuestionId() == null) {
            return questionVo;
        }

        // 查询问卷的所有选项
        List<SysQuestionOptionsVo> options = Arrays.stream(questionVo.getOptions().split(","))
            .map(option -> questionOptionsMapper.selectVoById(Long.parseLong(option)))
            .filter(Objects::nonNull)
            .toList();
        questionVo.setQuestionOptions(options);

        // 查询问卷的所有题目
        List<SysQuestionItemsVo> questionItems = questionItemsMapper.selectVoList(
            Wrappers.<SysQuestionItems>lambdaQuery()
                .eq(SysQuestionItems::getQuestionId, questionVo.getQuestionId())
                .orderByAsc(SysQuestionItems::getSort)
        );

        questionVo.setQuestionItems(questionItems);
        return questionVo;
    }

    /**
     * 新增问卷管理
     *
     * @param bo 问卷管理
     * @return 是否新增成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean insertByBo(SysQuestionBo bo) {
        SysQuestion add = MapstructUtils.convert(bo, SysQuestion.class);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            deleteAndInsertBatchItems(add.getQuestionId(), bo.getQuestionItems());
        }
        return flag;
    }

    /**
     * 修改问卷管理
     *
     * @param bo 问卷管理
     * @return 是否修改成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean updateByBo(SysQuestionBo bo) {
        SysQuestion update = MapstructUtils.convert(bo, SysQuestion.class);
        boolean flag = baseMapper.updateById(update) > 0;
        if (flag) {
            deleteAndInsertBatchItems(update.getQuestionId(), bo.getQuestionItems());
        }
        return flag;
    }

    private void deleteAndInsertBatchItems(Long questionId, List<SysQuestionItemsBo> questionItemsBoList) {
        if (CollUtil.isEmpty(questionItemsBoList)) {
            throw new ServiceException("题目列表不能为空");
        }
        List<SysQuestionItems> sysQuestionItems = questionItemsMapper.selectList(Wrappers.<SysQuestionItems>lambdaQuery().eq(SysQuestionItems::getQuestionId, questionId));
        if (CollUtil.isNotEmpty(sysQuestionItems)) {
            List<Long> itemIds = sysQuestionItems.stream().map(SysQuestionItems::getQuestionItemsId).toList();
            questionItemsMapper.deleteByIds(itemIds);
        }

        for (SysQuestionItemsBo questionItemsBo : questionItemsBoList) {
            questionItemsBo.setQuestionId(questionId);
            SysQuestionItems questionItems = MapstructUtils.convert(questionItemsBo, SysQuestionItems.class);
            questionItemsMapper.insert(questionItems);
        }
    }

    /**
     * 校验并批量删除问卷管理信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (ids == null || ids.isEmpty()) {
            return false;
        }

        // 1. 查询要删除的问卷相关的所有答卷ID
        List<Long> answerIds = questionAnswersMapper.selectList(
            new LambdaQueryWrapper<SysQuestionAnswers>()
                .in(SysQuestionAnswers::getQuestionId, ids)
        ).stream().map(SysQuestionAnswers::getAnswersId).toList();

        // 2. 删除答卷详情数据
        if (!answerIds.isEmpty()) {
            answerDetailsMapper.delete(
                new LambdaQueryWrapper<SysQuestionAnswerDetails>()
                    .in(SysQuestionAnswerDetails::getAnswerId, answerIds)
            );
        }

        // 3. 删除业主答卷数据
        questionAnswersMapper.delete(
            new LambdaQueryWrapper<SysQuestionAnswers>()
                .in(SysQuestionAnswers::getQuestionId, ids)
        );


        // 4. 删除题目数据
        questionItemsMapper.delete(
            new LambdaQueryWrapper<SysQuestionItems>()
                .in(SysQuestionItems::getQuestionId, ids)
        );

        // 5. 最后删除问卷主表数据
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public void exportAnswerDetail(HttpServletResponse response, Long questionId) {
        if (questionId == null || questionId <= 0) {
            throw new ServiceException("问卷主键错误");
        }
        SysQuestionVo sysQuestion = this.queryById(questionId);
        if (sysQuestion == null) {
            throw new ServiceException("问卷不存在");
        }
        Map<Long, SysOwner> ownerMap = ownerMapper.selectList(Wrappers.<SysOwner>lambdaQuery().eq(SysOwner::getHousingId, sysQuestion.getHousingId()))
                .stream().collect(Collectors.toMap(SysOwner::getOwnerId, Function.identity()));
        if (ownerMap.isEmpty()) {
            throw new ServiceException("没有找到该小区的业主信息");
        }
        Map<Long, List<SysOwnerHouse>> ownerHouseMap = ownerHouseMapper.selectList(new LambdaQueryWrapper<SysOwnerHouse>().eq(SysOwnerHouse::getHousingId, sysQuestion.getHousingId())
                .in(SysOwnerHouse::getOwnerId, ownerMap.keySet()))
            .stream().collect(Collectors.groupingBy(SysOwnerHouse::getOwnerId));

        List<SysQuestionOptionsVo> questionOptions = sysQuestion.getQuestionOptions();
        Map<Long, SysQuestionOptionsVo> optionsMap = questionOptions.stream().collect(Collectors.toMap(SysQuestionOptionsVo::getOptionsId, Function.identity()));

        List<SysQuestionItems> questionItems = questionItemsMapper.selectList(Wrappers.<SysQuestionItems>lambdaQuery().eq(SysQuestionItems::getQuestionId, questionId).orderByAsc(SysQuestionItems::getSort));

        Map<Long, SysQuestionAnswers> answersMap = questionAnswersMapper.selectList(Wrappers.<SysQuestionAnswers>lambdaQuery().eq(SysQuestionAnswers::getQuestionId, questionId))
            .stream().collect(Collectors.toMap(SysQuestionAnswers::getOwnerId, Function.identity()));

        List<Long> answersIds = answersMap.values().stream().map(SysQuestionAnswers::getAnswersId).toList();
        Map<Long, List<SysQuestionAnswerDetails>> answersDetailMap = answersIds.isEmpty() ? Collections.emptyMap() : answerDetailsMapper.selectList(Wrappers.<SysQuestionAnswerDetails>lambdaQuery().in(SysQuestionAnswerDetails::getAnswerId, answersIds))
            .stream().collect(Collectors.groupingBy(SysQuestionAnswerDetails::getAnswerId));

        // 获取小区名称
        SysHousing housing = housingMapper.selectById(sysQuestion.getHousingId());
        String housingName = housing != null ? housing.getHousingName() : "";

        List<Map<String, Object>> dataList = new ArrayList<>();

        for (Map.Entry<Long, SysOwner> ownerEntry : ownerMap.entrySet()) {
            Map<String, Object> map = new HashMap<>();
            dataList.add(map);

            map.put("questionTitle", sysQuestion.getQuestionTitle());
            map.put("questionRemark", sysQuestion.getQuestionRemark());
            map.put("questionOptions", sysQuestion.getQuestionOptions().stream().map(SysQuestionOptionsVo::getOptionContent).toList());
            map.put("signature", sysQuestion.getSignature());
            map.put("housingName", housingName);

            SysOwner owner = ownerEntry.getValue();
            map.put("ownerName", owner.getOwnerName());
            map.put("ownerPhone", owner.getOwnerPhone());
            // 处理房屋信息，将SysOwnerHouse对象转换为字符串
            List<SysOwnerHouse> ownerHouses = ownerHouseMap.getOrDefault(ownerEntry.getKey(), Collections.emptyList());
            List<String> houseInfos = ownerHouses.stream().map(sysOwnerHouse -> Optional.ofNullable(sysOwnerHouse.getCluster()).orElse("0") + "-" + sysOwnerHouse.getBuilding() + "-" + sysOwnerHouse.getCell() + "-" + sysOwnerHouse.getRoom()).toList();
            map.put("houseInfo", StrUtil.join("，", houseInfos));
            // 处理每个题目的答案信息
            List<Map<String, Object>> answersInfos = questionItems.stream().map(items -> {
                Map<String, Object> detailMap = new HashMap<>();
                detailMap.put("itemContent", items.getContent());
                detailMap.put("itemType", items.getItemType());
                if (answersMap.containsKey(ownerEntry.getKey())) {
                    SysQuestionAnswers answers = answersMap.get(ownerEntry.getKey());
                    List<SysQuestionAnswerDetails> answerDetails = answersDetailMap.get(answers.getAnswersId());
                    answerDetails.stream().filter(detail -> detail.getItemId().equals(items.getQuestionItemsId()))
                        .findFirst().ifPresent(entry -> {
                            if (items.getItemType() == 1 || items.getItemType() == 3) {
                                // 多选题用英文逗号隔开
                                detailMap.put("answer", Arrays.stream(entry.getOptionAnswer().split(",")).map(x -> optionsMap.get(Long.parseLong(x)).getOptionContent()).collect(Collectors.joining(",")));
                            } else {
                                detailMap.put("answer", entry.getTextAnswer());
                            }
                        });
                }
                return detailMap;
            }).toList();
            map.put("answersInfos", answersInfos);
            // 添加签名和答题信息
            if (answersMap.containsKey(ownerEntry.getKey())) {
                SysQuestionAnswers answers = answersMap.get(ownerEntry.getKey());
                if (answers.getSignImg() != null) {
                    // 添加签名图片
                    map.put("signImg", ossService.selectUrlByIds(answers.getSignImg().toString()));
                }
                // 添加答题日期
                map.put("answerDate", answers.getCreateTime());
            }
        }

        try {
            // 配置FreeMarker
            TemplateEngine engine = TemplateUtil.createEngine(new TemplateConfig("/", TemplateConfig.ResourceMode.CLASSPATH));

            // 尝试使用专门的PDF模板
            Template template = engine.getTemplate("template/question-survey.ftl");

            // 准备模板数据
            Map<String, Object> templateData = new HashMap<>();
            templateData.put("dataList", dataList);

            log.info("开始渲染模板，数据列表大小: {}", dataList.size());
            // 打印第一个数据项的中文内容用于调试
            if (!dataList.isEmpty()) {
                Map<String, Object> firstData = dataList.get(0);
                log.info("第一个数据项包含中文内容: 问卷标题={}, 小区名称={}, 业主姓名={}",
                    firstData.get("questionTitle"), firstData.get("housingName"), firstData.get("ownerName"));
            }

            // 渲染HTML内容
            StringWriter stringWriter = new StringWriter();
            template.render(templateData, stringWriter);
            String htmlContent = stringWriter.toString();

            log.info("HTML内容生成成功，长度: {}", htmlContent.length());

            // 检查HTML内容是否包含中文字符（使用更准确的方法）
            boolean containsChinese = htmlContent.chars()
                .anyMatch(ch -> ch >= 0x4E00 && ch <= 0x9FFF);
            log.info("HTML内容是否包含中文字符: {}", containsChinese);

            // 输出HTML内容片段用于验证
            String htmlPreview = htmlContent.length() > 200 ?
                htmlContent.substring(0, 200) + "..." : htmlContent;
            log.info("HTML内容预览: {}", htmlPreview);

            // 生成文件名
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String fileName = "问卷调查表_" + sysQuestion.getQuestionTitle() + "_" + timestamp + ".pdf";
            String outputPath = "D:/Project/housing/" + fileName;

            // 同时保存HTML文件用于调试
            String htmlPath = "D:/Project/housing/" + "问卷调查表_" + sysQuestion.getQuestionTitle() + "_" + timestamp + ".html";
            try (FileWriter htmlWriter = new FileWriter(htmlPath, StandardCharsets.UTF_8)) {
                htmlWriter.write(htmlContent);
                log.info("HTML文件已保存到: {}", htmlPath);
            }

            // 生成PDF文件
            log.info("开始调用generatePdfFromHtml方法生成PDF");
            generatePdfFromHtml(htmlContent, outputPath);
            log.info("PDF文件已生成: {}", outputPath);

            // 设置响应头，提供下载
            response.setContentType("application/pdf");
            response.setHeader("Content-Disposition", "attachment; filename=" + new String(fileName.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1));

            // 读取生成的PDF文件并写入响应
            try (FileInputStream fis = new FileInputStream(outputPath);
                 OutputStream os = response.getOutputStream()) {
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = fis.read(buffer)) != -1) {
                    os.write(buffer, 0, bytesRead);
                }
                os.flush();
            }
        } catch (Exception e) {
            log.error("导出问卷调查表失败", e);
            throw new ServiceException("导出失败：" + e.getMessage());
        }
    }

    /**
     * 将HTML内容转换为PDF文件
     *
     * @param htmlContent HTML内容
     * @param outputPath  输出文件路径
     */
    private void generatePdfFromHtml(String htmlContent, String outputPath) throws IOException {
        try (FileOutputStream fos = new FileOutputStream(outputPath)) {
            log.info("开始生成PDF，配置中文字体支持");

            // 创建转换属性配置
            ConverterProperties converterProperties = new ConverterProperties();

            // 创建字体提供器
            FontProvider fontProvider = new FontProvider();

            // 添加标准字体支持
            fontProvider.addStandardPdfFonts();

            // 尝试添加中文字体支持，使用多种策略
            boolean chineseFontAdded = false;

            // 调试：列出Windows字体目录中的字体文件
            try {
                java.io.File fontsDir = new java.io.File("C:/Windows/Fonts");
                if (fontsDir.exists() && fontsDir.isDirectory()) {
                    java.io.File[] fontFiles = fontsDir.listFiles((dir, name) ->
                        name.toLowerCase().contains("sim") || name.toLowerCase().contains("song") ||
                        name.toLowerCase().contains("msyh") || name.toLowerCase().contains("hei"));
                    if (fontFiles != null && fontFiles.length > 0) {
                        log.info("发现的中文字体文件:");
                        for (java.io.File file : fontFiles) {
                            log.info("  - {}", file.getName());
                        }
                    }
                }
            } catch (Exception e) {
                log.debug("列出字体文件时出错: {}", e.getMessage());
            }

            // 策略1：优先尝试加载用户系统中存在的SimsunExtG字体文件
            String simsunExtGPath = "C:/Windows/Fonts/SimsunExtG.ttf";
            java.io.File fontFile = new java.io.File(simsunExtGPath);
            if (fontFile.exists()) {
                try {
                    log.info("发现SimsunExtG字体文件，优先加载: {}", simsunExtGPath);
                    fontProvider.addFont(simsunExtGPath);
                    chineseFontAdded = true;
                    log.info("成功加载SimsunExtG字体文件");
                } catch (Exception e) {
                    log.warn("加载SimsunExtG字体文件失败: {}", e.getMessage());
                }
            } else {
                log.info("SimsunExtG字体文件不存在: {}", simsunExtGPath);
            }

            if (!chineseFontAdded) {
                // 策略2：尝试使用font-asian依赖中的多种字体
                String[] asianFonts = {"STSong-Light", "STSongStd-Light", "MSung-Light", "MHei-Medium"};
                for (String fontName : asianFonts) {
                    try {
                        log.info("尝试加载font-asian中的字体: {}", fontName);
                        fontProvider.addFont(fontName);
                        chineseFontAdded = true;
                        log.info("成功加载font-asian字体: {}", fontName);
                        break;
                    } catch (Exception e) {
                        log.warn("加载font-asian字体{}失败: {}", fontName, e.getMessage());
                    }
                }
            }

            if (!chineseFontAdded) {
                try {
                    // 策略3：尝试使用常见的中文字体名称
                    log.info("尝试加载SimSun字体");
                    fontProvider.addFont("SimSun");
                    chineseFontAdded = true;
                    log.info("成功加载SimSun字体");
                } catch (Exception e) {
                    log.warn("加载SimSun字体失败: {}", e.getMessage());
                }
            }

            if (!chineseFontAdded) {
                try {
                    // 策略4：尝试加载其他常见的Windows中文字体
                    String[] windowsFonts = {
                        "C:/Windows/Fonts/SimsunExtG.ttf", // 宋体扩展G（用户系统中存在的字体）
                        "C:/Windows/Fonts/msyh.ttc",       // 微软雅黑
                        "C:/Windows/Fonts/simhei.ttf",     // 黑体
                        "C:/Windows/Fonts/simsun.ttc",     // 宋体
                        "C:/Windows/Fonts/simsun.ttf"      // 宋体TTF格式
                    };
                    for (String fontPath : windowsFonts) {
                        java.io.File currentFontFile = new java.io.File(fontPath);
                        if (currentFontFile.exists()) {
                            try {
                                log.info("发现字体文件，尝试加载: {}", fontPath);
                                fontProvider.addFont(fontPath);
                                chineseFontAdded = true;
                                log.info("成功加载Windows字体文件: {}", fontPath);
                                break;
                            } catch (Exception fe) {
                                log.warn("加载Windows字体文件{}失败: {}", fontPath, fe.getMessage());
                            }
                        } else {
                            log.debug("字体文件不存在: {}", fontPath);
                        }
                    }
                } catch (Exception e) {
                    log.warn("加载Windows字体文件时出错: {}", e.getMessage());
                }
            }

            if (!chineseFontAdded) {
                log.warn("所有中文字体加载策略都失败，将仅使用标准字体，中文可能显示为方块");
                log.warn("建议检查系统是否安装了中文字体，或者font-asian依赖是否正确配置");
                log.warn("当前系统中发现的字体文件请查看上方日志");
            }

            // 设置字体提供器
            converterProperties.setFontProvider(fontProvider);

            // 验证字体配置
            log.info("字体配置完成，中文字体加载状态: {}", chineseFontAdded);
            log.info("FontProvider配置完成，开始转换HTML到PDF");

            // 使用配置的转换属性进行PDF转换
            HtmlConverter.convertToPdf(htmlContent, fos, converterProperties);

            log.info("PDF生成成功，已配置中文字体支持");
        } catch (Exception e) {
            log.error("PDF生成失败", e);
            throw new IOException("PDF生成失败: " + e.getMessage(), e);
        }

        // 验证生成的PDF文件
        try {
            java.io.File pdfFile = new java.io.File(outputPath);
            if (pdfFile.exists()) {
                long fileSize = pdfFile.length();
                log.info("PDF文件生成完成，文件大小: {} bytes", fileSize);
                if (fileSize == 0) {
                    log.error("PDF文件大小为0，可能生成失败");
                }
            } else {
                log.error("PDF文件不存在: {}", outputPath);
            }
        } catch (Exception e) {
            log.warn("验证PDF文件时出错: {}", e.getMessage());
        }
    }

    /**
     * App端查询问卷列表
     *
     * @param housingId 小区ID
     * @param userId    用户ID
     * @param pageQuery 分页参数
     * @return 问卷分页列表
     */
    @Override
    public TableDataInfo<SysQuestionVo> appQueryList(Long housingId, Long userId, PageQuery pageQuery) {
        // 查询指定小区的问卷列表
        SysQuestionBo bo = new SysQuestionBo();
        bo.setHousingId(housingId);
        Date nowDate = DateUtils.getNowDate();
        bo.setQuestionStartTime(nowDate);
        bo.setQuestionEndTime(nowDate);
        LambdaQueryWrapper<SysQuestion> lqw = buildQueryWrapper(bo);
        Page<SysQuestionVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);

        List<SysQuestionVo> questionList = result.getRecords();
        if (questionList.isEmpty()) {
            return TableDataInfo.build(result);
        }

        // 批量查询用户的答卷状态
        List<Long> questionIds = questionList.stream().map(SysQuestionVo::getQuestionId).toList();

        Set<Long> votedQuestionIds = questionAnswersMapper.selectList(
            Wrappers.<SysQuestionAnswers>lambdaQuery()
                .eq(SysQuestionAnswers::getOwnerId, userId)
                .in(SysQuestionAnswers::getQuestionId, questionIds)
        ).stream().map(SysQuestionAnswers::getQuestionId).collect(Collectors.toSet());

        // 设置投票状态
        questionList.forEach(question -> {
            question.setIsVoted(votedQuestionIds.contains(question.getQuestionId()) ? 1 : 0);
        });

        result.setRecords(questionList);
        return TableDataInfo.build(result);
    }

    /**
     * App端查询问卷详情
     *
     * @param questionId 问卷ID
     * @param userId     用户ID
     * @return 问卷详情
     */
    @Override
    public SysQuestionVo appQueryById(Long questionId, Long userId) {
        SysQuestionVo questionVo = baseMapper.selectVoById(questionId);
        if (questionVo == null) {
            return null;
        }

        return buildAppQuestionWithItems(questionVo, userId);
    }

    /**
     * 为App端构建问卷详情，包含用户答案
     *
     * @param questionVo 问卷VO
     * @param userId     用户ID
     * @return 包含用户答案的问卷详情
     */
    private SysQuestionVo buildAppQuestionWithItems(SysQuestionVo questionVo, Long userId) {
        buildQuestionWithItems(questionVo);

        // 检查用户是否已投票
        SysQuestionAnswers userAnswer = questionAnswersMapper.selectOne(
            Wrappers.<SysQuestionAnswers>lambdaQuery()
                .eq(SysQuestionAnswers::getQuestionId, questionVo.getQuestionId())
                .eq(SysQuestionAnswers::getOwnerId, userId)
        );

        questionVo.setIsVoted(userAnswer != null ? 1 : 0);

        if (userAnswer != null) {
            // 获取用户答案详情
            Map<Long, String> userAnswerMap = getUserAnswerMap(userAnswer.getAnswersId());
            // 为每个题目设置用户答案
            questionVo.getQuestionItems().forEach(item -> {
                String answerContent = userAnswerMap.get(item.getQuestionItemsId());
                item.setUserAnswer(answerContent);
            });
        }
        return questionVo;
    }

    /**
     * 获取用户答案映射
     *
     * @param answerId 答卷ID
     * @return 题目ID -> 答案内容的映射
     */
    private Map<Long, String> getUserAnswerMap(Long answerId) {
        List<SysQuestionAnswerDetails> answerDetails = answerDetailsMapper.selectList(
            Wrappers.<SysQuestionAnswerDetails>lambdaQuery()
                .eq(SysQuestionAnswerDetails::getAnswerId, answerId)
        );

        Map<Long, String> answerMap = new HashMap<>();
        answerDetails.forEach(detail -> {
            String answer = StringUtils.isNotBlank(detail.getOptionAnswer())
                ? detail.getOptionAnswer()
                : detail.getTextAnswer();
            if (StringUtils.isNotBlank(answer)) {
                answerMap.put(detail.getItemId(), answer);
            }
        });

        return answerMap;
    }

}
