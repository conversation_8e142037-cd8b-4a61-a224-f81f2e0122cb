package org.housing.system.controller.system;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.housing.common.core.constant.GlobalConstants;
import org.housing.common.core.domain.R;
import org.housing.common.core.domain.model.LoginUser;
import org.housing.common.core.validate.AddGroup;
import org.housing.common.log.annotation.Log;
import org.housing.common.log.enums.BusinessType;
import org.housing.common.mybatis.core.page.PageQuery;
import org.housing.common.mybatis.core.page.TableDataInfo;
import org.housing.common.redis.utils.RedisUtils;
import org.housing.common.satoken.utils.LoginHelper;
import org.housing.common.tenant.helper.TenantHelper;
import org.housing.common.web.core.BaseController;
import org.housing.system.domain.bo.*;
import org.housing.system.domain.vo.*;
import org.housing.system.service.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 微信管理
 *
 * <AUTHOR>
 * @since 2024-10-21
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/wechat")
public class WechatController extends BaseController {

    private final ISysOwnerService sysOwnerService;
    private final ISysNoticeService sysNoticeService;
    private final ISysVoteService sysVoteService;
    private final ISysTopicService sysTopicService;
    private final ISysDictTypeService sysDictTypeService;
    private final ISysOwnerVoteService sysOwnerVoteService;
    private final ISysProjectService sysProjectService;
    private final ISysProjectEntryService sysProjectEntryService;
    private final ISysQuestionService sysQuestionService;
    private final ISysQuestionAnswersService sysQuestionAnswersService;

    /**
     * 获取个人信息
     */
    @GetMapping("/getInfo")
    public R<SysOwnerVo> getInfo() {
        LoginUser loginUser = LoginHelper.getLoginUser();
        SysOwnerVo sysOwnerVo = sysOwnerService.queryById(loginUser.getUserId());
        return R.ok(sysOwnerVo);
    }

    /**
     * 获取公告列表
     */
    @GetMapping("/getNotices")
    public TableDataInfo<SysNoticeVo> getNotices(SysNoticeBo notice, PageQuery pageQuery) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        notice.setHousingId(loginUser.getHousingId());
        notice.setNowTime(new Date());
        return sysNoticeService.selectPageNoticeList(notice, pageQuery);
    }

    /**
     * 获取公告详情
     */
    @GetMapping("/getNoticeDetail")
    public R<SysNoticeVo> getNoticeDetail(@RequestParam Long noticeId) {
        SysNoticeVo sysNoticeVo = sysNoticeService.selectNoticeById(noticeId);
        return R.ok(sysNoticeVo);
    }

    /**
     * 获取议题列表
     */
    @GetMapping("/getTopics")
    public TableDataInfo<SysTopicVo> getTopics(SysTopicBo bo, PageQuery pageQuery) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        bo.setHousingId(loginUser.getHousingId());
        return sysTopicService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取议题详情
     */
    @GetMapping("/getTopicDetail")
    public R<SysTopicVo> getTopicDetail(@RequestParam Long topicId) {
        SysTopicVo sysTopicVo = sysTopicService.queryById(topicId);
        return R.ok(sysTopicVo);
    }

    /**
     * 获取投票列表
     */
    @GetMapping("/getVotes")
    public R<List<SysVoteVo>> getVotes(@RequestParam Long topicId) {
        Long userId = LoginHelper.getUserId();
        List<SysVoteVo> sysVoteVoList = sysVoteService.selectByTopicId(topicId, userId);
        return R.ok(sysVoteVoList);
    }

    /**
     * 投票类型列表
     */
    @GetMapping(value = "/voteType")
    public R<List<SysDictDataVo>> voteType() {
        List<SysDictDataVo> data = sysDictTypeService.selectDictDataByType("sys_vote_advice");
        if (ObjectUtil.isNull(data)) {
            data = new ArrayList<>();
        }
        return R.ok(data);
    }

    /**
     * 业主投票
     */
    @Log(title = "业主投票", businessType = BusinessType.INSERT)
    @PostMapping("/voteAdd")
    public R<Void> voteAdd(@Validated(AddGroup.class) @RequestBody SysOwnerVoteBo bo) {
        bo.setResource("1");
        return toAjax(sysOwnerVoteService.insertByBo(bo));
    }

    /**
     * 获取项目工程列表
     */
    @GetMapping("/projects")
    public TableDataInfo<SysProjectVo> projects(SysProjectBo bo, PageQuery pageQuery) {
        TableDataInfo<SysProjectVo> dataInfo = TenantHelper.ignore(() -> sysProjectService.queryPageList(bo, pageQuery));
        if (CollUtil.isNotEmpty(dataInfo.getRows())) {
            dataInfo.getRows().forEach(this::setEntryStatus);
        }
        return dataInfo;
    }

    /**
     * 项目工程详情
     */
    @GetMapping("/projectDetail")
    public R<SysProjectVo> projectDetail(@RequestParam Long projectId) {
        SysProjectVo projectVo = TenantHelper.ignore(() -> sysProjectService.queryById(projectId));
        if (projectVo != null) {
            setEntryStatus(projectVo);
        }
        return R.ok(projectVo);
    }

    /**
     * 项目报名
     */
    @Log(title = "H5报名", businessType = BusinessType.INSERT)
    @PostMapping("/projectEntry")
    public R<Void> projectEntry(@Validated(AddGroup.class) @RequestBody SysProjectEntryBo bo) {
        String key = GlobalConstants.CAPTCHA_CODE_KEY + bo.getPhoneNumber();
        String code = RedisUtils.getCacheObject(key);
        if (StrUtil.isBlank(code)) {
            return R.fail("验证码已过期");
        }
        if (!code.equals(bo.getSmsCode())) {
            return R.fail("验证码错误");
        }
        SysProjectVo projectVo = TenantHelper.ignore(() -> sysProjectService.queryById(bo.getProjectId()));
        if (projectVo == null) {
            return R.fail("项目不存在");
        }
        bo.setTenantId(projectVo.getTenantId());
        bo.setEntryUserId(LoginHelper.getUserId());
        return toAjax(TenantHelper.ignore(() -> sysProjectEntryService.insertByBo(bo)));
    }

    /**
     * 获取问卷列表
     */
    @GetMapping("/getQuestions")
    public TableDataInfo<SysQuestionVo> getQuestions(PageQuery pageQuery) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        return sysQuestionService.appQueryList(loginUser.getHousingId(), loginUser.getUserId(), pageQuery);
    }

    /**
     * 获取问卷详情
     */
    @GetMapping("/getQuestionDetail")
    public R<SysQuestionVo> getQuestionDetail(@RequestParam Long questionId) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        SysQuestionVo sysQuestionVo = sysQuestionService.appQueryById(questionId, loginUser.getUserId());
        return R.ok(sysQuestionVo);
    }

    /**
     * 业主答卷
     */
    @Log(title = "业主答卷", businessType = BusinessType.INSERT)
    @PostMapping("/answerAdd")
    public R<Void> answerAdd(@Validated(AddGroup.class) @RequestBody SysOwnerAnswerBo bo) {
        System.out.println("=== 后端接收到的数据 ===");
        System.out.println("bo.getQuestionId(): " + bo.getQuestionId());
        System.out.println("bo.getESignImg(): " + bo.getESignImg());
        System.out.println("bo.getAnswers(): " + bo.getAnswers());
        System.out.println("bo对象完整信息: " + bo.toString());
        
        bo.setOwnerId(LoginHelper.getUserId());
        return toAjax(sysQuestionAnswersService.insertByBo(bo));
    }

    private void setEntryStatus(SysProjectVo projectVo) {
        Long userId = LoginHelper.getUserId();
        Boolean exist = TenantHelper.ignore(() -> sysProjectEntryService.existUser(projectVo.getProjectId(), userId));
        if (exist) {
            projectVo.setEntryStatus("已报名");
        } else {
            projectVo.setEntryStatus("未报名");
        }
    }
}
