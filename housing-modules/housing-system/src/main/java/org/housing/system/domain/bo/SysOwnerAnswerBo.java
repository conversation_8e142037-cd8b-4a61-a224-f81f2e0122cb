package org.housing.system.domain.bo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.housing.common.core.validate.AddGroup;

import java.io.Serial;
import java.io.Serializable;
import java.util.Map;

/**
 * description:
 * create: 2025/7/23
 * author: chen bin
 */
@Data
public class SysOwnerAnswerBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @NotNull(message = "问卷主键不能为空", groups = { AddGroup.class })
    private Long questionId;

    private Long ownerId;

    private String eSignImg;

    @NotNull(message = "问卷答案不能为空", groups = { AddGroup.class })
    private Map<Long, String> answers;
}
