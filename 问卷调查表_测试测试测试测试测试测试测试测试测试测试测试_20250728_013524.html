<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="zh-CN">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <title>业主意见征询表</title>
    <style>


        body {
            font-family: serif;
            font-size: 12px;
            line-height: 1.5;
            margin: 0;
            padding: 20px;
            background: white;
        }

        .page {
            width: 100%;
            margin: 0 auto;
            background: white;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 15px;
        }

        .title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .basic-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .basic-info span {
            flex: 1;
        }

        .satisfaction-section {
            margin-bottom: 30px;
        }

        .satisfaction-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            table-layout: fixed;
        }

        .satisfaction-table th,
        .satisfaction-table td {
            border: 1px solid #000;
            padding: 8px;
            text-align: center;
            font-size: 12px;
            word-wrap: break-word;
        }

        .satisfaction-table th {
            background-color: #ffffff;
            font-weight: bold;
            color: #000000;
        }

        .satisfaction-table .item-name {
            text-align: left;
            width: 200px;
            font-weight: normal;
        }

        .satisfaction-table .question-header {
            text-align: center;
            width: 200px;
            font-weight: bold;
            color: #000000;
        }

        .satisfaction-table .option-header {
            text-align: center;
            width: 80px;
            font-weight: bold;
            color: #000000;
        }

        .satisfaction-table .option-cell {
            text-align: center;
            width: 80px;
        }

        .checkbox {
            width: 15px;
            height: 15px;
            border: 1px solid #000;
            display: inline-block;
            text-align: center;
            line-height: 13px;
            margin: 0 2px;
        }

        .checkbox.checked::before {
            content: "✓";
            font-weight: bold;
            color: #000000;
        }

        .question-section {
            margin-bottom: 20px;
        }

        .question-title {
            font-weight: bold;
            margin-bottom: 10px;
            font-size: 14px;
            color: #000000;
        }

        .question-item {
            margin-bottom: 15px;
            padding: 10px;
            border: 1px solid #ddd;
        }

        .text-answer {
            border-bottom: 1px solid #000;
            min-height: 20px;
            padding: 5px;
            margin-top: 5px;
        }

        .signature-section {
            margin-top: 40px;
        }

        .signature-item {
            margin-bottom: 30px;
            position: relative;
        }

        .signature-label {
            display: inline-block;
            background: white;
            padding-right: 10px;
            position: relative;
            z-index: 1;
        }

        .signature-line {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 1px;
            border-bottom: 1px solid #000;
        }

        .signature-image {
            display: inline-block;
            margin-left: 20px;
        }

        .sign-img {
            max-width: 150px;
            max-height: 80px;
            border: 1px solid #ddd;
            padding: 5px;
        }

        .date-section {
            text-align: right;
            margin-top: 30px;
        }

        .underline {
            border-bottom: 1px solid #000;
            display: inline-block;
            min-width: 100px;
            text-align: center;
        }


    </style>
</head>
<body>
        <div class="page">
        <!-- 页面头部 -->
        <div class="header">
            <div class="title">测试测试测试测试测试测试测试测试测试测试测试</div>
        </div>

        <!-- 基本信息 -->
        <div class="basic-info">
            <span>物业名称：<span class="underline">测试</span></span>
            <span>房号：<span class="underline">0-1-1-111</span></span>
            <span>联系方式：<span class="underline">19955925890</span></span>
        </div>

        <!-- 选择题和多选题表格 -->

        <div class="satisfaction-section">
            <table class="satisfaction-table">
                <thead>
                    <tr>
                        <th class="question-header">题目</th>
                        <th class="option-header">非常满意</th>
                        <th class="option-header">满意</th>
                        <th class="option-header">一般</th>
                        <th class="option-header">不满意</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="item-name">测试1</td>
                        <td class="option-cell">
                            <span class="checkbox "></span>
                        </td>
                        <td class="option-cell">
                            <span class="checkbox "></span>
                        </td>
                        <td class="option-cell">
                            <span class="checkbox "></span>
                        </td>
                        <td class="option-cell">
                            <span class="checkbox "></span>
                        </td>
                    </tr>
                    <tr>
                        <td class="item-name">测试2</td>
                        <td class="option-cell">
                            <span class="checkbox "></span>
                        </td>
                        <td class="option-cell">
                            <span class="checkbox "></span>
                        </td>
                        <td class="option-cell">
                            <span class="checkbox "></span>
                        </td>
                        <td class="option-cell">
                            <span class="checkbox "></span>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 填空题部分 -->

        <div class="question-section">
            <div class="question-item">
                <div class="question-title">测试3</div>
                <div class="text-answer">
                </div>
            </div>
        </div>



        <!-- 签名区域 -->
        <div class="signature-section">
            <div class="signature-item">
                <span class="signature-label">物业服务中心工作人员：</span>
                <div class="signature-line"></div>
            </div>
            <div class="signature-item">
                <span class="signature-label">业主本人签名：</span>
                    <div class="signature-line"></div>
            </div>
        </div>

        <!-- 日期 -->
        <div class="date-section">
                <span style="margin-right: 50px;">年</span>
                <span style="margin-right: 50px;">月</span>
                <span>日</span>
        </div>
    </div>
    <div class="page">
        <!-- 页面头部 -->
        <div class="header">
            <div class="title">测试测试测试测试测试测试测试测试测试测试测试</div>
        </div>

        <!-- 基本信息 -->
        <div class="basic-info">
            <span>物业名称：<span class="underline">测试</span></span>
            <span>房号：<span class="underline">0-0-0-0</span></span>
            <span>联系方式：<span class="underline">18767560318</span></span>
        </div>

        <!-- 选择题和多选题表格 -->

        <div class="satisfaction-section">
            <table class="satisfaction-table">
                <thead>
                    <tr>
                        <th class="question-header">题目</th>
                        <th class="option-header">非常满意</th>
                        <th class="option-header">满意</th>
                        <th class="option-header">一般</th>
                        <th class="option-header">不满意</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="item-name">测试1</td>
                        <td class="option-cell">
                            <span class="checkbox "></span>
                        </td>
                        <td class="option-cell">
                            <span class="checkbox "></span>
                        </td>
                        <td class="option-cell">
                            <span class="checkbox "></span>
                        </td>
                        <td class="option-cell">
                            <span class="checkbox "></span>
                        </td>
                    </tr>
                    <tr>
                        <td class="item-name">测试2</td>
                        <td class="option-cell">
                            <span class="checkbox "></span>
                        </td>
                        <td class="option-cell">
                            <span class="checkbox "></span>
                        </td>
                        <td class="option-cell">
                            <span class="checkbox "></span>
                        </td>
                        <td class="option-cell">
                            <span class="checkbox "></span>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 填空题部分 -->

        <div class="question-section">
            <div class="question-item">
                <div class="question-title">测试3</div>
                <div class="text-answer">
                </div>
            </div>
        </div>



        <!-- 签名区域 -->
        <div class="signature-section">
            <div class="signature-item">
                <span class="signature-label">物业服务中心工作人员：</span>
                <div class="signature-line"></div>
            </div>
            <div class="signature-item">
                <span class="signature-label">业主本人签名：</span>
                    <div class="signature-line"></div>
            </div>
        </div>

        <!-- 日期 -->
        <div class="date-section">
                <span style="margin-right: 50px;">年</span>
                <span style="margin-right: 50px;">月</span>
                <span>日</span>
        </div>
    </div>
    <div class="page">
        <!-- 页面头部 -->
        <div class="header">
            <div class="title">测试测试测试测试测试测试测试测试测试测试测试</div>
        </div>

        <!-- 基本信息 -->
        <div class="basic-info">
            <span>物业名称：<span class="underline">测试</span></span>
            <span>房号：<span class="underline">0-1-1-103</span></span>
            <span>联系方式：<span class="underline">13587444371</span></span>
        </div>

        <!-- 选择题和多选题表格 -->

        <div class="satisfaction-section">
            <table class="satisfaction-table">
                <thead>
                    <tr>
                        <th class="question-header">题目</th>
                        <th class="option-header">非常满意</th>
                        <th class="option-header">满意</th>
                        <th class="option-header">一般</th>
                        <th class="option-header">不满意</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="item-name">测试1</td>
                        <td class="option-cell">
                            <span class="checkbox checked"></span>
                        </td>
                        <td class="option-cell">
                            <span class="checkbox "></span>
                        </td>
                        <td class="option-cell">
                            <span class="checkbox "></span>
                        </td>
                        <td class="option-cell">
                            <span class="checkbox "></span>
                        </td>
                    </tr>
                    <tr>
                        <td class="item-name">测试2</td>
                        <td class="option-cell">
                            <span class="checkbox checked"></span>
                        </td>
                        <td class="option-cell">
                            <span class="checkbox checked"></span>
                        </td>
                        <td class="option-cell">
                            <span class="checkbox "></span>
                        </td>
                        <td class="option-cell">
                            <span class="checkbox "></span>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 填空题部分 -->

        <div class="question-section">
            <div class="question-item">
                <div class="question-title">测试3</div>
                <div class="text-answer">
                        测试
                </div>
            </div>
        </div>



        <!-- 签名区域 -->
        <div class="signature-section">
            <div class="signature-item">
                <span class="signature-label">物业服务中心工作人员：</span>
                <div class="signature-line"></div>
            </div>
            <div class="signature-item">
                <span class="signature-label">业主本人签名：</span>
                    <div class="signature-image">
                        <img src="https://housing-oss.ninthone.cn/zhaofu/2025/07/27/f16421ad83054aeba850c23193d75011.png" alt="业主签名" class="sign-img" />
                    </div>
            </div>
        </div>

        <!-- 日期 -->
        <div class="date-section">
                <span style="margin-right: 50px;">2025年</span>
                <span style="margin-right: 50px;">07月</span>
                <span>27日</span>
        </div>
    </div>
</body>
</html>
