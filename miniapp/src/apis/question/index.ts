import request
    from '@/utils/request';
import type * as T
    from './model';

// 获取问卷列表
export const getQuestions = (params?: {
  current?: number;
  pageSize?: number;
}) =>
  request<T.IQuestionListResponse>({
    url: '/wechat/getQuestions',
    method: 'GET',
    params,
  });

// 获取问卷详情
export const getQuestionDetail = (questionId: string) =>
  request<T.IQuestionDetailResponse>({
    url: '/wechat/getQuestionDetail',
    method: 'GET',
    params: {
      questionId
    }
  });

// 业主答卷
export const answerAdd = async (data: T.IOwnerAnswerRequest) => {
  // 使用Taro.request而不是自定义request，避免Content-Type问题
  const baseUrl = process.env.BUILD_ENV === 'prod' 
    ? 'https://housing-admin.ninthone.cn/prod-api'
    : 'http://127.0.0.1:8080';
    
  const tokenKey = `TP-token-${process.env.BUILD_ENV}`;
  const token = Taro.getStorageSync(tokenKey);
  
  console.log('answerAdd - 使用Taro.request直接发送');
  
  const response = await Taro.request({
    url: `${baseUrl}/wechat/answerAdd`,
    method: 'POST',
    data: data,
    header: {
      'content-type': 'application/json',
      'Authorization': token ? `Bearer ${token}` : ''
    }
  });
  
  console.log('answerAdd - Taro.request响应:', response);
  
  if (response.statusCode === 200 && response.data.code === 200) {
    return response.data.data;
  } else {
    throw new Error(response.data?.msg || '请求失败');
  }
};
