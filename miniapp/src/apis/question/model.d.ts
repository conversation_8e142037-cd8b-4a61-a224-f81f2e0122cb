// 问卷相关类型定义

// 问卷项目选项
export interface IQuestionOption {
  optionsId: string | number;
  optionContent: string;
  isDefault: number;
  sort: number;
  housingId: string | number;
  housingName: string;
}

// 问卷项目
export interface IQuestionItem {
  questionItemsId: string | number;
  questionId: string | number;
  itemType: number;
  content: string;
  sort: number;
  isRequired: number;
  userAnswer?: string;
}

// 问卷信息
export interface IQuestion {
  questionId: string; // 改为字符串类型避免精度丢失
  questionTitle: string;
  questionRemark: string;
  signature: number;
  housingId: number;
  createTime: string;
  questionItems: IQuestionItem[];
  housingName: string;
  questionOptions: IQuestionOption[];
  isVoted: number; // 0-未投票 1-已投票
  questionStartTime: string;
  questionEndTime: string;
}

// 问卷列表响应
export interface IQuestionListResponse {
  rows: IQuestion[];
  total: number;
}

// 问卷详情响应
export interface IQuestionDetailResponse extends IQuestion {}

// 业主答卷请求参数
export interface IOwnerAnswerRequest {
  questionId: string | number; // 支持字符串或数字，后端能自动转换
  ownerId?: number;
  signImg?: string | number; // 支持字符串或数字，后端能自动转换
  answers: Record<string | number, string>; // 键支持字符串或数字格式
}
